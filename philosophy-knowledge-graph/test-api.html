<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, button { padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        input[type="text"] { width: 300px; }
        button { background: #007bff; color: white; cursor: pointer; }
        button:hover { background: #0056b3; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .result { margin-top: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 4px; }
        .loading { color: #007bff; }
        .error { color: #dc3545; }
        .success { color: #28a745; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>哲学知识图谱 API 测试</h1>
        
        <form id="testForm">
            <div class="form-group">
                <label for="topic">哲学主题:</label>
                <input type="text" id="topic" value="存在主义" required>
            </div>
            
            <div class="form-group">
                <label for="depth">分析深度:</label>
                <select id="depth">
                    <option value="1">1 - 简单</option>
                    <option value="2" selected>2 - 中等</option>
                    <option value="3">3 - 详细</option>
                    <option value="4">4 - 深入</option>
                    <option value="5">5 - 专家</option>
                </select>
            </div>
            
            <button type="submit" id="submitBtn">测试 API</button>
        </form>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const resultDiv = document.getElementById('result');
            const topic = document.getElementById('topic').value;
            const depth = parseInt(document.getElementById('depth').value);
            
            // 显示加载状态
            submitBtn.disabled = true;
            submitBtn.textContent = '分析中...';
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="loading">正在调用 OpenRouter API 分析中，请稍候...</div>';
            
            try {
                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        topic: topic,
                        depth: depth,
                        language: 'zh',
                        includeHistorical: true,
                        includeContemporary: true
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ API 调用成功!</div>
                        <h3>分析结果:</h3>
                        <p><strong>主题:</strong> ${data.graph.topic}</p>
                        <p><strong>概念数量:</strong> ${data.graph.nodes.length}</p>
                        <p><strong>关系数量:</strong> ${data.graph.edges.length}</p>
                        <p><strong>置信度:</strong> ${Math.round(data.confidence * 100)}%</p>
                        <p><strong>处理时间:</strong> ${data.processingTime}ms</p>
                        
                        <h4>核心概念:</h4>
                        <ul>
                            ${data.graph.nodes.map(node => 
                                `<li><strong>${node.name}</strong> (重要性: ${Math.round(node.importance * 100)}%) - ${node.description}</li>`
                            ).join('')}
                        </ul>
                        
                        <h4>AI 洞察:</h4>
                        <ul>
                            ${data.insights.map(insight => `<li>${insight}</li>`).join('')}
                        </ul>
                        
                        <h4>学习建议:</h4>
                        <ul>
                            ${data.suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                        </ul>
                        
                        <details>
                            <summary>完整 JSON 响应</summary>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </details>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ API 调用失败: ${data.error}</div>`;
                }
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '测试 API';
            }
        });
    </script>
</body>
</html>
