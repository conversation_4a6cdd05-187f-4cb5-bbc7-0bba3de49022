import { 
  AnalysisRequest, 
  KnowledgeGraph,
  ConceptNode,
  ConceptEdge,
  PhilosophyCategory,
  RelationType,
  Philosopher,
  PhilosophicalWork 
} from '@/types/knowledge-graph';

// AI分析结果接口
interface AIAnalysisResult {
  graph: KnowledgeGraph;
  insights: string[];
  suggestions: string[];
  confidence: number;
}

// 哲学概念模板库
const philosophyTemplates = {
  '存在主义': {
    category: PhilosophyCategory.EXISTENTIALISM,
    coreConcepts: [
      { name: '存在先于本质', importance: 0.9, level: 1 },
      { name: '自由选择', importance: 0.8, level: 1 },
      { name: '焦虑', importance: 0.7, level: 2 },
      { name: '真实性', importance: 0.7, level: 2 },
      { name: '坏信仰', importance: 0.6, level: 2 },
      { name: '荒诞', importance: 0.8, level: 1 },
      { name: '责任', importance: 0.7, level: 2 }
    ],
    philosophers: [
      { name: '让-保罗·萨特', contribution: '存在先于本质理论' },
      { name: '阿尔贝·加缪', contribution: '荒诞主义哲学' },
      { name: '索伦·克尔凯郭尔', contribution: '焦虑概念的先驱' },
      { name: '马丁·海德格尔', contribution: '此在概念' }
    ]
  },
  '道德哲学': {
    category: PhilosophyCategory.ETHICS,
    coreConcepts: [
      { name: '功利主义', importance: 0.9, level: 1 },
      { name: '义务论', importance: 0.9, level: 1 },
      { name: '德性伦理学', importance: 0.8, level: 1 },
      { name: '道德相对主义', importance: 0.7, level: 2 },
      { name: '道德实在论', importance: 0.7, level: 2 },
      { name: '最大幸福原则', importance: 0.6, level: 2 },
      { name: '绝对命令', importance: 0.8, level: 2 }
    ],
    philosophers: [
      { name: '伊曼努尔·康德', contribution: '绝对命令理论' },
      { name: '约翰·斯图尔特·密尔', contribution: '功利主义发展' },
      { name: '亚里士多德', contribution: '德性伦理学' },
      { name: '杰里米·边沁', contribution: '功利主义创立' }
    ]
  },
  '自由意志': {
    category: PhilosophyCategory.PHILOSOPHY_OF_MIND,
    coreConcepts: [
      { name: '决定论', importance: 0.9, level: 1 },
      { name: '非决定论', importance: 0.8, level: 1 },
      { name: '相容论', importance: 0.8, level: 1 },
      { name: '不相容论', importance: 0.7, level: 2 },
      { name: '因果决定论', importance: 0.7, level: 2 },
      { name: '道德责任', importance: 0.8, level: 2 },
      { name: '选择的自由', importance: 0.6, level: 2 }
    ],
    philosophers: [
      { name: '大卫·休谟', contribution: '相容论观点' },
      { name: '伊曼努尔·康德', contribution: '超验自由概念' },
      { name: '威廉·詹姆斯', contribution: '非决定论立场' },
      { name: '丹尼尔·丹尼特', contribution: '现代相容论' }
    ]
  }
};

/**
 * 生成哲学知识图谱
 */
export async function generatePhilosophyGraph(request: AnalysisRequest): Promise<AIAnalysisResult> {
  const { topic, depth, focus, language = 'zh', includeHistorical = true, includeContemporary = true } = request;

  // 模拟AI分析过程
  await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

  // 获取主题模板
  const template = philosophyTemplates[topic as keyof typeof philosophyTemplates];
  
  if (!template) {
    // 如果没有预定义模板，生成通用结构
    return generateGenericPhilosophyGraph(request);
  }

  // 基于模板生成概念节点
  const nodes: ConceptNode[] = [];
  
  // 添加主题根节点
  const rootNode: ConceptNode = {
    id: generateId(topic),
    name: topic,
    description: `${topic}的核心概念和理论体系`,
    category: template.category,
    importance: 1.0,
    level: 0,
    metadata: {
      philosophers: template.philosophers.map(p => ({
        id: generateId(p.name),
        name: p.name,
        nameEn: '',
        lifespan: '',
        nationality: '',
        school: [],
        mainContributions: [p.contribution]
      })),
      works: [],
      historicalPeriod: includeHistorical ? '古代至现代' : '现代',
      schools: [topic],
      keywords: [topic],
      difficulty: Math.min(depth + 1, 5),
      popularity: 0.8
    }
  };
  nodes.push(rootNode);

  // 添加核心概念节点
  template.coreConcepts.slice(0, depth * 2).forEach(concept => {
    const node: ConceptNode = {
      id: generateId(concept.name),
      name: concept.name,
      description: generateConceptDescription(concept.name, topic),
      category: template.category,
      importance: concept.importance,
      level: concept.level,
      metadata: {
        philosophers: template.philosophers.slice(0, 2).map(p => ({
          id: generateId(p.name),
          name: p.name,
          nameEn: '',
          lifespan: '',
          nationality: '',
          school: [],
          mainContributions: [p.contribution]
        })),
        works: [],
        historicalPeriod: includeHistorical ? '19-20世纪' : '现代',
        schools: [topic],
        keywords: [concept.name, topic],
        difficulty: Math.min(concept.level + 2, 5),
        popularity: concept.importance * 0.8
      }
    };
    nodes.push(node);
  });

  // 生成关系边
  const edges: ConceptEdge[] = [];
  
  // 根节点与一级概念的关系
  nodes.filter(n => n.level === 1).forEach(node => {
    edges.push({
      id: `edge-${rootNode.id}-${node.id}`,
      source: rootNode.id,
      target: node.id,
      type: RelationType.CONTAINS,
      strength: 0.9,
      description: `${topic}包含${node.name}概念`,
      bidirectional: false
    });
  });

  // 概念间的关系
  const level1Nodes = nodes.filter(n => n.level === 1);
  const level2Nodes = nodes.filter(n => n.level === 2);
  
  level2Nodes.forEach(level2Node => {
    const relatedLevel1 = level1Nodes[Math.floor(Math.random() * level1Nodes.length)];
    edges.push({
      id: `edge-${relatedLevel1.id}-${level2Node.id}`,
      source: relatedLevel1.id,
      target: level2Node.id,
      type: getRandomRelationType(),
      strength: 0.6 + Math.random() * 0.3,
      description: `${relatedLevel1.name}与${level2Node.name}相关`,
      bidirectional: Math.random() > 0.5
    });
  });

  // 构建知识图谱
  const graph: KnowledgeGraph = {
    id: generateId(`${topic}-graph`),
    topic,
    description: `关于${topic}的知识图谱分析`,
    nodes,
    edges,
    metadata: {
      totalConcepts: nodes.length,
      maxDepth: Math.max(...nodes.map(n => n.level)),
      categories: [template.category],
      complexity: Math.min(depth * 2, 10),
      completeness: Math.min(0.6 + depth * 0.1, 1.0),
      sources: ['AI生成', '哲学百科全书']
    },
    createdAt: new Date(),
    updatedAt: new Date()
  };

  // 生成洞察和建议
  const insights = generateInsights(topic, nodes.length, template.category);
  const suggestions = generateSuggestions(topic, depth);

  return {
    graph,
    insights,
    suggestions,
    confidence: 0.75 + Math.random() * 0.2
  };
}

/**
 * 生成通用哲学图谱（当没有预定义模板时）
 */
function generateGenericPhilosophyGraph(request: AnalysisRequest): AIAnalysisResult {
  const { topic, depth } = request;
  
  const nodes: ConceptNode[] = [{
    id: generateId(topic),
    name: topic,
    description: `${topic}的相关概念和理论`,
    category: PhilosophyCategory.CONTEMPORARY_PHILOSOPHY,
    importance: 1.0,
    level: 0,
    metadata: {
      philosophers: [],
      works: [],
      historicalPeriod: '现代',
      schools: [topic],
      keywords: [topic],
      difficulty: 3,
      popularity: 0.5
    }
  }];

  const edges: ConceptEdge[] = [];

  const graph: KnowledgeGraph = {
    id: generateId(`${topic}-graph`),
    topic,
    description: `关于${topic}的基础知识图谱`,
    nodes,
    edges,
    metadata: {
      totalConcepts: 1,
      maxDepth: 0,
      categories: [PhilosophyCategory.CONTEMPORARY_PHILOSOPHY],
      complexity: 3,
      completeness: 0.3,
      sources: ['AI生成']
    },
    createdAt: new Date(),
    updatedAt: new Date()
  };

  return {
    graph,
    insights: [`${topic}是一个值得深入探索的哲学主题`],
    suggestions: [`建议进一步研究${topic}的历史发展`, `探索${topic}与其他哲学概念的关系`],
    confidence: 0.5
  };
}

// 辅助函数
function generateId(name: string): string {
  return name.toLowerCase().replace(/[^a-z0-9\u4e00-\u9fa5]/g, '-') + '-' + Math.random().toString(36).substr(2, 9);
}

function generateConceptDescription(concept: string, topic: string): string {
  const descriptions: { [key: string]: string } = {
    '存在先于本质': '萨特提出的核心理念，认为人首先存在，然后通过选择和行动创造自己的本质',
    '自由选择': '存在主义强调的人类根本特征，认为人在任何情况下都有选择的自由',
    '焦虑': '面对自由和责任时产生的根本性情感体验',
    '真实性': '按照自己的本真存在方式生活，不受外在期望和社会角色束缚',
    '坏信仰': '个体为逃避自由和责任而采取的自欺行为',
    '荒诞': '人类寻求意义的努力与世界无意义本质之间的根本冲突',
    '责任': '伴随自由选择而来的对自己行为后果的承担'
  };
  
  return descriptions[concept] || `${concept}是${topic}中的重要概念`;
}

function getRandomRelationType(): RelationType {
  const types = [
    RelationType.INFLUENCES,
    RelationType.RELATES,
    RelationType.DEVELOPS,
    RelationType.APPLIES,
    RelationType.DERIVES
  ];
  return types[Math.floor(Math.random() * types.length)];
}

function generateInsights(topic: string, nodeCount: number, category: PhilosophyCategory): string[] {
  return [
    `${topic}包含${nodeCount}个核心概念，形成了复杂的理论体系`,
    `该主题属于${category}范畴，具有深刻的哲学意义`,
    `概念间的相互关系展现了${topic}的内在逻辑结构`,
    `这些概念在现代哲学讨论中仍然具有重要价值`
  ];
}

function generateSuggestions(topic: string, depth: number): string[] {
  const suggestions = [
    `深入研究${topic}的历史发展脉络`,
    `探索${topic}与其他哲学分支的关系`,
    `阅读相关的经典哲学著作`,
    `思考${topic}在当代社会中的应用价值`
  ];
  
  if (depth < 3) {
    suggestions.push(`增加分析深度以获得更全面的理解`);
  }
  
  return suggestions;
}
