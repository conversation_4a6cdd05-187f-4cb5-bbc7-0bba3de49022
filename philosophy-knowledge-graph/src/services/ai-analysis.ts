import { 
  AnalysisRequest, 
  KnowledgeGraph,
  ConceptNode,
  ConceptEdge,
  PhilosophyCategory,
  RelationType,
  Philosopher,
  PhilosophicalWork 
} from '@/types/knowledge-graph';

// AI分析结果接口
interface AIAnalysisResult {
  graph: KnowledgeGraph;
  insights: string[];
  suggestions: string[];
  confidence: number;
}

// 哲学概念模板库
const philosophyTemplates = {
  '存在主义': {
    category: PhilosophyCategory.EXISTENTIALISM,
    coreConcepts: [
      { name: '存在先于本质', importance: 0.9, level: 1 },
      { name: '自由选择', importance: 0.8, level: 1 },
      { name: '焦虑', importance: 0.7, level: 2 },
      { name: '真实性', importance: 0.7, level: 2 },
      { name: '坏信仰', importance: 0.6, level: 2 },
      { name: '荒诞', importance: 0.8, level: 1 },
      { name: '责任', importance: 0.7, level: 2 }
    ],
    philosophers: [
      { name: '让-保罗·萨特', contribution: '存在先于本质理论' },
      { name: '阿尔贝·加缪', contribution: '荒诞主义哲学' },
      { name: '索伦·克尔凯郭尔', contribution: '焦虑概念的先驱' },
      { name: '马丁·海德格尔', contribution: '此在概念' }
    ]
  },
  '道德哲学': {
    category: PhilosophyCategory.ETHICS,
    coreConcepts: [
      { name: '功利主义', importance: 0.9, level: 1 },
      { name: '义务论', importance: 0.9, level: 1 },
      { name: '德性伦理学', importance: 0.8, level: 1 },
      { name: '道德相对主义', importance: 0.7, level: 2 },
      { name: '道德实在论', importance: 0.7, level: 2 },
      { name: '最大幸福原则', importance: 0.6, level: 2 },
      { name: '绝对命令', importance: 0.8, level: 2 }
    ],
    philosophers: [
      { name: '伊曼努尔·康德', contribution: '绝对命令理论' },
      { name: '约翰·斯图尔特·密尔', contribution: '功利主义发展' },
      { name: '亚里士多德', contribution: '德性伦理学' },
      { name: '杰里米·边沁', contribution: '功利主义创立' }
    ]
  },
  '自由意志': {
    category: PhilosophyCategory.PHILOSOPHY_OF_MIND,
    coreConcepts: [
      { name: '决定论', importance: 0.9, level: 1 },
      { name: '非决定论', importance: 0.8, level: 1 },
      { name: '相容论', importance: 0.8, level: 1 },
      { name: '不相容论', importance: 0.7, level: 2 },
      { name: '因果决定论', importance: 0.7, level: 2 },
      { name: '道德责任', importance: 0.8, level: 2 },
      { name: '选择的自由', importance: 0.6, level: 2 }
    ],
    philosophers: [
      { name: '大卫·休谟', contribution: '相容论观点' },
      { name: '伊曼努尔·康德', contribution: '超验自由概念' },
      { name: '威廉·詹姆斯', contribution: '非决定论立场' },
      { name: '丹尼尔·丹尼特', contribution: '现代相容论' }
    ]
  }
};

/**
 * 调用OpenRouter API生成哲学概念分析
 */
async function callOpenRouterAPI(prompt: string): Promise<string> {
  const apiKey = process.env.OPENROUTER_API_KEY;
  const model = process.env.OPENROUTER_MODEL || 'google/gemini-2.5-pro';
  const baseUrl = process.env.OPENROUTER_BASE_URL || 'https://openrouter.ai/api/v1';

  if (!apiKey) {
    throw new Error('OpenRouter API key not configured');
  }

  try {
    const response = await fetch(`${baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'http://localhost:3000',
        'X-Title': 'Philosophy Knowledge Graph'
      },
      body: JSON.stringify({
        model: model,
        messages: [
          {
            role: 'system',
            content: '你是一位专业的哲学教授和知识图谱专家。请根据用户提供的哲学主题，分析其核心概念、子概念和相互关系，并以结构化的JSON格式返回。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 4000
      })
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0]?.message?.content || '';
  } catch (error) {
    console.error('OpenRouter API call failed:', error);
    throw error;
  }
}

/**
 * 生成哲学知识图谱
 */
export async function generatePhilosophyGraph(request: AnalysisRequest): Promise<AIAnalysisResult> {
  const { topic, depth, focus, language = 'zh', includeHistorical = true, includeContemporary = true } = request;

  try {
    // 构建AI提示词
    const prompt = buildAnalysisPrompt(topic, depth, focus, includeHistorical, includeContemporary, language);
    console.log('🤖 Calling OpenRouter API for topic:', topic);

    // 调用OpenRouter API
    const aiResponse = await callOpenRouterAPI(prompt);
    console.log('📥 AI Response received, length:', aiResponse.length);
    console.log('📄 AI Response preview:', aiResponse.substring(0, 200) + '...');

    // 尝试简单的AI分析
    const simpleResult = createSimpleAIResult(aiResponse, topic, depth);
    if (simpleResult) {
      console.log('✅ Simple AI analysis successful');
      return simpleResult;
    }

    // 解析AI响应
    const parsedResult = parseAIResponse(aiResponse, topic);

    if (parsedResult) {
      console.log('✅ AI analysis successful, nodes:', parsedResult.graph.nodes.length);
      return parsedResult;
    }
  } catch (error) {
    console.error('❌ AI analysis failed, falling back to template:', error);
  }

  // 如果AI分析失败，回退到模板
  const template = philosophyTemplates[topic as keyof typeof philosophyTemplates];

  if (!template) {
    // 如果没有预定义模板，生成通用结构
    return generateGenericPhilosophyGraph(request);
  }

  // 基于模板生成概念节点
  const nodes: ConceptNode[] = [];
  
  // 添加主题根节点
  const rootNode: ConceptNode = {
    id: generateId(topic),
    name: topic,
    description: `${topic}的核心概念和理论体系`,
    category: template.category,
    importance: 1.0,
    level: 0,
    metadata: {
      philosophers: template.philosophers.map(p => ({
        id: generateId(p.name),
        name: p.name,
        nameEn: '',
        lifespan: '',
        nationality: '',
        school: [],
        mainContributions: [p.contribution]
      })),
      works: [],
      historicalPeriod: includeHistorical ? '古代至现代' : '现代',
      schools: [topic],
      keywords: [topic],
      difficulty: Math.min(depth + 1, 5),
      popularity: 0.8
    }
  };
  nodes.push(rootNode);

  // 添加核心概念节点
  template.coreConcepts.slice(0, depth * 2).forEach(concept => {
    const node: ConceptNode = {
      id: generateId(concept.name),
      name: concept.name,
      description: generateConceptDescription(concept.name, topic),
      category: template.category,
      importance: concept.importance,
      level: concept.level,
      metadata: {
        philosophers: template.philosophers.slice(0, 2).map(p => ({
          id: generateId(p.name),
          name: p.name,
          nameEn: '',
          lifespan: '',
          nationality: '',
          school: [],
          mainContributions: [p.contribution]
        })),
        works: [],
        historicalPeriod: includeHistorical ? '19-20世纪' : '现代',
        schools: [topic],
        keywords: [concept.name, topic],
        difficulty: Math.min(concept.level + 2, 5),
        popularity: concept.importance * 0.8
      }
    };
    nodes.push(node);
  });

  // 生成关系边
  const edges: ConceptEdge[] = [];
  
  // 根节点与一级概念的关系
  nodes.filter(n => n.level === 1).forEach(node => {
    edges.push({
      id: `edge-${rootNode.id}-${node.id}`,
      source: rootNode.id,
      target: node.id,
      type: RelationType.CONTAINS,
      strength: 0.9,
      description: `${topic}包含${node.name}概念`,
      bidirectional: false
    });
  });

  // 概念间的关系
  const level1Nodes = nodes.filter(n => n.level === 1);
  const level2Nodes = nodes.filter(n => n.level === 2);
  
  level2Nodes.forEach(level2Node => {
    const relatedLevel1 = level1Nodes[Math.floor(Math.random() * level1Nodes.length)];
    edges.push({
      id: `edge-${relatedLevel1.id}-${level2Node.id}`,
      source: relatedLevel1.id,
      target: level2Node.id,
      type: getRandomRelationType(),
      strength: 0.6 + Math.random() * 0.3,
      description: `${relatedLevel1.name}与${level2Node.name}相关`,
      bidirectional: Math.random() > 0.5
    });
  });

  // 构建知识图谱
  const graph: KnowledgeGraph = {
    id: generateId(`${topic}-graph`),
    topic,
    description: `关于${topic}的知识图谱分析`,
    nodes,
    edges,
    metadata: {
      totalConcepts: nodes.length,
      maxDepth: Math.max(...nodes.map(n => n.level)),
      categories: [template.category],
      complexity: Math.min(depth * 2, 10),
      completeness: Math.min(0.6 + depth * 0.1, 1.0),
      sources: ['AI生成', '哲学百科全书']
    },
    createdAt: new Date(),
    updatedAt: new Date()
  };

  // 生成洞察和建议
  const insights = generateInsights(topic, nodes.length, template.category);
  const suggestions = generateSuggestions(topic, depth);

  return {
    graph,
    insights,
    suggestions,
    confidence: 0.75 + Math.random() * 0.2
  };
}

/**
 * 生成通用哲学图谱（当没有预定义模板时）
 */
function generateGenericPhilosophyGraph(request: AnalysisRequest): AIAnalysisResult {
  const { topic, depth } = request;
  
  const nodes: ConceptNode[] = [{
    id: generateId(topic),
    name: topic,
    description: `${topic}的相关概念和理论`,
    category: PhilosophyCategory.CONTEMPORARY_PHILOSOPHY,
    importance: 1.0,
    level: 0,
    metadata: {
      philosophers: [],
      works: [],
      historicalPeriod: '现代',
      schools: [topic],
      keywords: [topic],
      difficulty: 3,
      popularity: 0.5
    }
  }];

  const edges: ConceptEdge[] = [];

  const graph: KnowledgeGraph = {
    id: generateId(`${topic}-graph`),
    topic,
    description: `关于${topic}的基础知识图谱`,
    nodes,
    edges,
    metadata: {
      totalConcepts: 1,
      maxDepth: 0,
      categories: [PhilosophyCategory.CONTEMPORARY_PHILOSOPHY],
      complexity: 3,
      completeness: 0.3,
      sources: ['AI生成']
    },
    createdAt: new Date(),
    updatedAt: new Date()
  };

  return {
    graph,
    insights: [`${topic}是一个值得深入探索的哲学主题`],
    suggestions: [`建议进一步研究${topic}的历史发展`, `探索${topic}与其他哲学概念的关系`],
    confidence: 0.5
  };
}

/**
 * 构建AI分析提示词
 */
function buildAnalysisPrompt(
  topic: string,
  depth: number,
  focus?: PhilosophyCategory[],
  includeHistorical?: boolean,
  includeContemporary?: boolean,
  language?: string
): string {
  const focusText = focus && focus.length > 0 ? `，特别关注${focus.join('、')}领域` : '';
  const historicalText = includeHistorical ? '包含历史发展脉络' : '';
  const contemporaryText = includeContemporary ? '包含当代观点' : '';

  return `请分析哲学主题"${topic}"，返回${depth * 2}个核心概念。

请严格按照以下JSON格式返回，不要添加任何解释文字：

{
  "concepts": [
    {
      "name": "概念名称",
      "description": "简短描述",
      "importance": 0.8,
      "level": 1
    }
  ],
  "relationships": [
    {
      "source": "概念1",
      "target": "概念2",
      "type": "contains",
      "strength": 0.8
    }
  ],
  "insights": ["洞察1", "洞察2"],
  "suggestions": ["建议1", "建议2"]
}

要求：
- 只返回JSON，不要markdown代码块
- 确保JSON格式完全正确
- 概念名称要简洁明确
- 关系类型只能是: contains, influences, opposes, relates`;
}

/**
 * 创建简单的AI分析结果（当JSON解析失败时）
 */
function createSimpleAIResult(aiResponse: string, topic: string, depth: number): AIAnalysisResult | null {
  try {
    // 从AI响应中提取概念名称
    const conceptMatches = aiResponse.match(/["""]([^"""]+)["""]/g) || [];
    const concepts = conceptMatches
      .map(match => match.replace(/["""]/g, '').trim())
      .filter(concept => concept.length > 2 && concept.length < 50)
      .slice(0, depth * 3); // 限制概念数量

    if (concepts.length === 0) {
      return null;
    }

    // 创建节点
    const nodes: ConceptNode[] = concepts.map((concept, index) => ({
      id: generateId(concept),
      name: concept,
      description: `${concept}是${topic}中的重要概念`,
      category: PhilosophyCategory.CONTEMPORARY_PHILOSOPHY,
      importance: Math.max(0.3, 1 - index * 0.1),
      level: Math.min(Math.floor(index / 2), depth),
      metadata: {
        philosophers: [],
        works: [],
        historicalPeriod: '现代',
        schools: [topic],
        keywords: [concept, topic],
        difficulty: Math.min(3 + Math.floor(index / 2), 5),
        popularity: Math.max(0.3, 1 - index * 0.1)
      }
    }));

    // 创建简单的关系
    const edges: ConceptEdge[] = [];
    for (let i = 1; i < nodes.length; i++) {
      edges.push({
        id: generateId(`${nodes[0].id}-${nodes[i].id}`),
        source: nodes[0].id,
        target: nodes[i].id,
        type: RelationType.RELATES,
        strength: 0.6,
        description: `${nodes[0].name}与${nodes[i].name}相关`,
        bidirectional: false
      });
    }

    const graph: KnowledgeGraph = {
      id: generateId(`${topic}-simple-ai-graph`),
      topic,
      description: `AI分析的${topic}概念图谱`,
      nodes,
      edges,
      metadata: {
        totalConcepts: nodes.length,
        maxDepth: Math.max(...nodes.map(n => n.level)),
        categories: [PhilosophyCategory.CONTEMPORARY_PHILOSOPHY],
        complexity: Math.min(nodes.length, 8),
        completeness: 0.7,
        sources: ['AI分析', 'OpenRouter API']
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    return {
      graph,
      insights: [
        `AI识别了${nodes.length}个与${topic}相关的核心概念`,
        `这些概念展现了${topic}的多维度特征`,
        `建议深入研究每个概念的具体内涵`
      ],
      suggestions: [
        `进一步探索${topic}的历史发展`,
        `阅读相关的经典哲学著作`,
        `思考这些概念在现代社会中的应用`
      ],
      confidence: 0.75
    };

  } catch (error) {
    console.error('Simple AI analysis failed:', error);
    return null;
  }
}

/**
 * 解析AI响应
 */
function parseAIResponse(aiResponse: string, topic: string): AIAnalysisResult | null {
  try {
    // 清理响应文本
    let cleanResponse = aiResponse.trim();

    // 尝试多种方式提取JSON
    let jsonStr = '';

    // 方法1: 寻找完整的JSON对象
    const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      jsonStr = jsonMatch[0];
    } else {
      // 方法2: 寻找```json代码块
      const codeBlockMatch = cleanResponse.match(/```json\s*([\s\S]*?)\s*```/);
      if (codeBlockMatch) {
        jsonStr = codeBlockMatch[1];
      } else {
        // 方法3: 寻找```代码块
        const generalCodeMatch = cleanResponse.match(/```\s*([\s\S]*?)\s*```/);
        if (generalCodeMatch) {
          jsonStr = generalCodeMatch[1];
        } else {
          throw new Error('No JSON found in AI response');
        }
      }
    }

    // 尝试修复常见的JSON格式问题
    jsonStr = jsonStr
      .replace(/,\s*}/g, '}')  // 移除对象末尾的逗号
      .replace(/,\s*]/g, ']')  // 移除数组末尾的逗号
      .replace(/'/g, '"')      // 替换单引号为双引号
      .replace(/\n/g, ' ')     // 移除换行符
      .replace(/\s+/g, ' ')    // 合并多个空格
      .trim();

    // 尝试多次解析，逐步修复JSON
    let parsed;
    try {
      parsed = JSON.parse(jsonStr);
    } catch (firstError) {
      console.log('First JSON parse failed, trying to fix...', firstError.message);

      // 尝试修复更多问题
      try {
        // 移除可能的尾随逗号和其他问题
        let fixedJson = jsonStr
          .replace(/,(\s*[}\]])/g, '$1')  // 移除尾随逗号
          .replace(/([{,]\s*)(\w+):/g, '$1"$2":')  // 给属性名加引号
          .replace(/:\s*([^",\[\]{}\s]+)(\s*[,}\]])/g, ':"$1"$2'); // 给字符串值加引号

        parsed = JSON.parse(fixedJson);
      } catch (secondError) {
        console.log('Second JSON parse failed, trying simpler approach...', secondError.message);

        // 如果还是失败，尝试提取部分有效的JSON
        try {
          // 尝试只解析concepts部分
          const conceptsMatch = jsonStr.match(/"concepts"\s*:\s*\[([\s\S]*?)\]/);
          if (conceptsMatch) {
            const conceptsStr = `{"concepts":[${conceptsMatch[1]}]}`;
            const conceptsFixed = conceptsStr.replace(/,(\s*[}\]])/g, '$1');
            const conceptsData = JSON.parse(conceptsFixed);

            parsed = {
              topic: topic,
              concepts: conceptsData.concepts || [],
              relationships: [],
              insights: [`AI分析了${topic}的核心概念`],
              suggestions: [`深入研究${topic}的相关理论`]
            };
          } else {
            throw new Error('Cannot extract valid JSON');
          }
        } catch (thirdError) {
          console.log('All JSON parsing attempts failed:', thirdError.message);
          throw new Error('Failed to parse AI response after multiple attempts');
        }
      }
    }

    // 转换为我们的数据结构
    const nodes: ConceptNode[] = parsed.concepts?.map((concept: any, index: number) => ({
      id: generateId(concept.name),
      name: concept.name,
      description: concept.description,
      category: PhilosophyCategory.EXISTENTIALISM, // 默认分类
      importance: concept.importance || 0.5,
      level: concept.level || Math.min(index, 2),
      metadata: {
        philosophers: concept.philosophers?.map((p: any) => ({
          id: generateId(p.name),
          name: p.name,
          nameEn: '',
          lifespan: '',
          nationality: '',
          school: [],
          mainContributions: [p.contribution || '']
        })) || [],
        works: concept.works?.map((w: any) => ({
          id: generateId(w.title || ''),
          title: w.title || '',
          titleEn: '',
          author: w.author || '',
          year: w.year || 2000,
          description: '',
          significance: ''
        })) || [],
        historicalPeriod: '现代',
        schools: [topic],
        keywords: concept.keywords || [concept.name],
        difficulty: Math.min((concept.level || 1) + 2, 5),
        popularity: concept.importance || 0.5
      }
    })) || [];

    // 创建名称到ID的映射
    const nameToId = new Map<string, string>();
    nodes.forEach(node => {
      nameToId.set(node.name, node.id);
    });

    const edges: ConceptEdge[] = parsed.relationships?.map((rel: any) => ({
      id: generateId(`${rel.source}-${rel.target}`),
      source: nameToId.get(rel.source) || generateId(rel.source),
      target: nameToId.get(rel.target) || generateId(rel.target),
      type: rel.type === 'contains' ? RelationType.CONTAINS :
            rel.type === 'influences' ? RelationType.INFLUENCES :
            rel.type === 'opposes' ? RelationType.OPPOSES :
            RelationType.RELATES,
      strength: rel.strength || 0.5,
      description: rel.description || '',
      bidirectional: false
    })) || [];

    const graph: KnowledgeGraph = {
      id: generateId(`${topic}-ai-graph`),
      topic,
      description: `AI生成的${topic}知识图谱`,
      nodes,
      edges,
      metadata: {
        totalConcepts: nodes.length,
        maxDepth: Math.max(...nodes.map(n => n.level)),
        categories: [...new Set(nodes.map(n => n.category))],
        complexity: Math.min(nodes.length, 10),
        completeness: 0.9,
        sources: ['AI生成', 'OpenRouter API']
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    return {
      graph,
      insights: parsed.insights || [`AI分析了${topic}的核心概念和关系`],
      suggestions: parsed.suggestions || [`深入研究${topic}的相关著作`],
      confidence: 0.85
    };

  } catch (error) {
    console.error('Failed to parse AI response:', error);
    return null;
  }
}

// 辅助函数
function generateId(name: string): string {
  return name.toLowerCase().replace(/[^a-z0-9\u4e00-\u9fa5]/g, '-') + '-' + Math.random().toString(36).substr(2, 9);
}

function generateConceptDescription(concept: string, topic: string): string {
  const descriptions: { [key: string]: string } = {
    '存在先于本质': '萨特提出的核心理念，认为人首先存在，然后通过选择和行动创造自己的本质',
    '自由选择': '存在主义强调的人类根本特征，认为人在任何情况下都有选择的自由',
    '焦虑': '面对自由和责任时产生的根本性情感体验',
    '真实性': '按照自己的本真存在方式生活，不受外在期望和社会角色束缚',
    '坏信仰': '个体为逃避自由和责任而采取的自欺行为',
    '荒诞': '人类寻求意义的努力与世界无意义本质之间的根本冲突',
    '责任': '伴随自由选择而来的对自己行为后果的承担'
  };
  
  return descriptions[concept] || `${concept}是${topic}中的重要概念`;
}

function getRandomRelationType(): RelationType {
  const types = [
    RelationType.INFLUENCES,
    RelationType.RELATES,
    RelationType.DEVELOPS,
    RelationType.APPLIES,
    RelationType.DERIVES
  ];
  return types[Math.floor(Math.random() * types.length)];
}

function generateInsights(topic: string, nodeCount: number, category: PhilosophyCategory): string[] {
  return [
    `${topic}包含${nodeCount}个核心概念，形成了复杂的理论体系`,
    `该主题属于${category}范畴，具有深刻的哲学意义`,
    `概念间的相互关系展现了${topic}的内在逻辑结构`,
    `这些概念在现代哲学讨论中仍然具有重要价值`
  ];
}

function generateSuggestions(topic: string, depth: number): string[] {
  const suggestions = [
    `深入研究${topic}的历史发展脉络`,
    `探索${topic}与其他哲学分支的关系`,
    `阅读相关的经典哲学著作`,
    `思考${topic}在当代社会中的应用价值`
  ];
  
  if (depth < 3) {
    suggestions.push(`增加分析深度以获得更全面的理解`);
  }
  
  return suggestions;
}
