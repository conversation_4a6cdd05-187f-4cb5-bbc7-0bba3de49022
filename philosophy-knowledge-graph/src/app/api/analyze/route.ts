import { NextRequest, NextResponse } from 'next/server';
import { 
  AnalysisRequest, 
  AnalysisResponse, 
  KnowledgeGraph,
  ConceptNode,
  ConceptEdge,
  PhilosophyCategory,
  RelationType 
} from '@/types/knowledge-graph';
import { generatePhilosophyGraph } from '@/services/ai-analysis';

export async function POST(request: NextRequest) {
  try {
    const body: AnalysisRequest = await request.json();
    
    // 验证请求参数
    if (!body.topic || body.topic.trim().length === 0) {
      return NextResponse.json(
        { error: '请提供有效的哲学主题' },
        { status: 400 }
      );
    }

    if (body.depth < 1 || body.depth > 5) {
      return NextResponse.json(
        { error: '分析深度必须在1-5之间' },
        { status: 400 }
      );
    }

    const startTime = Date.now();

    // 调用AI分析服务
    const analysisResult = await generatePhilosophyGraph(body);
    
    const processingTime = Date.now() - startTime;

    const response: AnalysisResponse = {
      success: true,
      graph: analysisResult.graph,
      insights: analysisResult.insights,
      suggestions: analysisResult.suggestions,
      confidence: analysisResult.confidence,
      processingTime
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('AI分析服务错误:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: '分析服务暂时不可用，请稍后重试',
        processingTime: 0
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: '哲学概念分析API',
    version: '1.0.0',
    endpoints: {
      analyze: {
        method: 'POST',
        description: '分析哲学主题并生成知识图谱',
        parameters: {
          topic: 'string - 哲学主题',
          depth: 'number - 分析深度 (1-5)',
          focus: 'array - 关注的哲学分类 (可选)',
          language: 'string - 语言偏好 (zh/en)',
          includeHistorical: 'boolean - 是否包含历史背景',
          includeContemporary: 'boolean - 是否包含当代观点'
        }
      }
    }
  });
}
