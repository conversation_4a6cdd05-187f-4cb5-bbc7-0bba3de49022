'use client';

import React from 'react';
import { ConceptNode, PhilosophyCategory } from '@/types/knowledge-graph';
import { getCategoryColor } from '@/utils/graph-utils';
import { X, User, Book, Clock, Tag, Star, Brain } from 'lucide-react';

interface ConceptDetailPanelProps {
  node: ConceptNode | null;
  isOpen: boolean;
  onClose: () => void;
}

const categoryNames: Record<PhilosophyCategory, string> = {
  [PhilosophyCategory.METAPHYSICS]: '形而上学',
  [PhilosophyCategory.EPISTEMOLOGY]: '认识论',
  [PhilosophyCategory.ETHICS]: '伦理学',
  [PhilosophyCategory.LOGIC]: '逻辑学',
  [PhilosophyCategory.AESTHETICS]: '美学',
  [PhilosophyCategory.POLITICAL_PHILOSOPHY]: '政治哲学',
  [PhilosophyCategory.PHILOSOPHY_OF_MIND]: '心灵哲学',
  [PhilosophyCategory.PHILOSOPHY_OF_RELIGION]: '宗教哲学',
  [PhilosophyCategory.EXISTENTIALISM]: '存在主义',
  [PhilosophyCategory.PHENOMENOLOGY]: '现象学',
  [PhilosophyCategory.ANALYTIC_PHILOSOPHY]: '分析哲学',
  [PhilosophyCategory.CONTINENTAL_PHILOSOPHY]: '大陆哲学',
  [PhilosophyCategory.EASTERN_PHILOSOPHY]: '东方哲学',
  [PhilosophyCategory.ANCIENT_PHILOSOPHY]: '古代哲学',
  [PhilosophyCategory.MODERN_PHILOSOPHY]: '现代哲学',
  [PhilosophyCategory.CONTEMPORARY_PHILOSOPHY]: '当代哲学'
};

const difficultyLabels = ['入门', '初级', '中级', '高级', '专家'];

export default function ConceptDetailPanel({ node, isOpen, onClose }: ConceptDetailPanelProps) {
  if (!isOpen || !node) {
    return null;
  }

  const categoryColor = getCategoryColor(node.category);
  const difficultyStars = Array.from({ length: 5 }, (_, i) => i < node.metadata.difficulty);

  return (
    <div className="fixed inset-y-0 right-0 w-full sm:w-96 bg-white shadow-2xl transform transition-transform duration-300 ease-in-out z-50 overflow-y-auto">
      {/* 头部 */}
      <div className="sticky top-0 bg-white border-b border-gray-200 p-3 sm:p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div 
              className="w-4 h-4 rounded-full"
              style={{ backgroundColor: categoryColor }}
            />
            <h2 className="text-xl font-bold text-gray-900 truncate">
              {node.name}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>
      </div>

      {/* 内容 */}
      <div className="p-4 space-y-6">
        {/* 基本信息 */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-3">概念描述</h3>
          <p className="text-gray-700 leading-relaxed">
            {node.description}
          </p>
        </div>

        {/* 分类和属性 */}
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <Tag className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-600">分类</span>
            </div>
            <span className="text-sm text-gray-900">
              {categoryNames[node.category]}
            </span>
          </div>

          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <Brain className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-600">层级</span>
            </div>
            <span className="text-sm text-gray-900">
              第 {node.level + 1} 层
            </span>
          </div>
        </div>

        {/* 重要性和难度 */}
        <div className="space-y-3">
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-600">重要程度</span>
              <span className="text-sm text-gray-900">
                {Math.round(node.importance * 100)}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${node.importance * 100}%` }}
              />
            </div>
          </div>

          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-600">难度等级</span>
              <span className="text-sm text-gray-900">
                {difficultyLabels[node.metadata.difficulty - 1]}
              </span>
            </div>
            <div className="flex gap-1">
              {difficultyStars.map((filled, index) => (
                <Star
                  key={index}
                  className={`w-4 h-4 ${
                    filled ? 'text-yellow-400 fill-current' : 'text-gray-300'
                  }`}
                />
              ))}
            </div>
          </div>

          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-600">受关注度</span>
              <span className="text-sm text-gray-900">
                {Math.round(node.metadata.popularity * 100)}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${node.metadata.popularity * 100}%` }}
              />
            </div>
          </div>
        </div>

        {/* 相关哲学家 */}
        {node.metadata.philosophers.length > 0 && (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
              <User className="w-5 h-5" />
              相关哲学家
            </h3>
            <div className="space-y-3">
              {node.metadata.philosophers.map((philosopher) => (
                <div key={philosopher.id} className="bg-gray-50 p-3 rounded-lg">
                  <div className="flex items-start justify-between">
                    <div>
                      <h4 className="font-medium text-gray-900">
                        {philosopher.name}
                      </h4>
                      {philosopher.nameEn && (
                        <p className="text-sm text-gray-600 italic">
                          {philosopher.nameEn}
                        </p>
                      )}
                      {philosopher.lifespan && (
                        <p className="text-sm text-gray-600">
                          {philosopher.lifespan}
                        </p>
                      )}
                    </div>
                  </div>
                  {philosopher.mainContributions.length > 0 && (
                    <div className="mt-2">
                      <p className="text-xs text-gray-500 mb-1">主要贡献：</p>
                      <ul className="text-sm text-gray-700 space-y-1">
                        {philosopher.mainContributions.map((contribution, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <span className="text-gray-400 mt-1">•</span>
                            <span>{contribution}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 相关著作 */}
        {node.metadata.works.length > 0 && (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
              <Book className="w-5 h-5" />
              相关著作
            </h3>
            <div className="space-y-3">
              {node.metadata.works.map((work) => (
                <div key={work.id} className="bg-gray-50 p-3 rounded-lg">
                  <h4 className="font-medium text-gray-900">
                    {work.title}
                  </h4>
                  {work.titleEn && (
                    <p className="text-sm text-gray-600 italic">
                      {work.titleEn}
                    </p>
                  )}
                  <div className="flex items-center gap-4 mt-1 text-sm text-gray-600">
                    <span>{work.author}</span>
                    <span>{work.year}</span>
                  </div>
                  <p className="text-sm text-gray-700 mt-2">
                    {work.description}
                  </p>
                  {work.significance && (
                    <div className="mt-2">
                      <p className="text-xs text-gray-500 mb-1">重要性：</p>
                      <p className="text-sm text-gray-700">
                        {work.significance}
                      </p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 历史时期和学派 */}
        <div className="grid grid-cols-1 gap-4">
          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-600">历史时期</span>
            </div>
            <span className="text-sm text-gray-900">
              {node.metadata.historicalPeriod}
            </span>
          </div>

          {node.metadata.schools.length > 0 && (
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Tag className="w-4 h-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-600">哲学流派</span>
              </div>
              <div className="flex flex-wrap gap-1">
                {node.metadata.schools.map((school, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-white text-xs text-gray-700 rounded border"
                  >
                    {school}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 关键词 */}
        {node.metadata.keywords.length > 0 && (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">关键词</h3>
            <div className="flex flex-wrap gap-2">
              {node.metadata.keywords.map((keyword, index) => (
                <span
                  key={index}
                  className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                >
                  {keyword}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
