'use client';

import React, { useState } from 'react';
import { AnalysisRequest, PhilosophyCategory } from '@/types/knowledge-graph';
import { Search, Settings, Sparkles } from 'lucide-react';

interface SearchInterfaceProps {
  onSearch: (request: AnalysisRequest) => void;
  isLoading: boolean;
}

const categoryOptions = [
  { value: PhilosophyCategory.EXISTENTIALISM, label: '存在主义' },
  { value: PhilosophyCategory.ETHICS, label: '伦理学' },
  { value: PhilosophyCategory.EPISTEMOLOGY, label: '认识论' },
  { value: PhilosophyCategory.METAPHYSICS, label: '形而上学' },
  { value: PhilosophyCategory.PHILOSOPHY_OF_MIND, label: '心灵哲学' },
  { value: PhilosophyCategory.POLITICAL_PHILOSOPHY, label: '政治哲学' },
  { value: PhilosophyCategory.LOGIC, label: '逻辑学' },
  { value: PhilosophyCategory.AESTHETICS, label: '美学' }
];

const popularTopics = [
  '存在主义',
  '道德哲学', 
  '自由意志',
  '心灵哲学',
  '认识论',
  '政治哲学',
  '美学理论',
  '逻辑学'
];

export default function SearchInterface({ onSearch, isLoading }: SearchInterfaceProps) {
  const [topic, setTopic] = useState('');
  const [depth, setDepth] = useState(3);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [selectedCategories, setSelectedCategories] = useState<PhilosophyCategory[]>([]);
  const [includeHistorical, setIncludeHistorical] = useState(true);
  const [includeContemporary, setIncludeContemporary] = useState(true);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!topic.trim()) return;

    const request: AnalysisRequest = {
      topic: topic.trim(),
      depth,
      focus: selectedCategories.length > 0 ? selectedCategories : undefined,
      language: 'zh',
      includeHistorical,
      includeContemporary
    };

    onSearch(request);
  };

  const handleTopicClick = (selectedTopic: string) => {
    setTopic(selectedTopic);
  };

  const toggleCategory = (category: PhilosophyCategory) => {
    setSelectedCategories(prev => 
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    );
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border p-4 sm:p-6">
      <div className="mb-4 sm:mb-6">
        <h2 className="text-base sm:text-lg font-semibold text-gray-900 mb-2 flex items-center gap-2">
          <Sparkles className="w-4 sm:w-5 h-4 sm:h-5 text-blue-500" />
          AI 哲学概念分析
        </h2>
        <p className="text-gray-600 text-xs sm:text-sm">
          输入您感兴趣的哲学主题，AI将为您生成详细的概念知识图谱
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
        {/* 主题输入 */}
        <div>
          <label htmlFor="topic" className="block text-xs sm:text-sm font-medium text-gray-700 mb-2">
            哲学主题
          </label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 sm:w-5 h-4 sm:h-5" />
            <input
              id="topic"
              type="text"
              value={topic}
              onChange={(e) => setTopic(e.target.value)}
              placeholder="例如：存在主义、道德哲学、自由意志..."
              className="w-full pl-9 sm:pl-10 pr-4 py-2 sm:py-3 text-sm sm:text-base border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isLoading}
            />
          </div>
        </div>

        {/* 热门主题 */}
        <div>
          <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-2">
            热门主题
          </label>
          <div className="flex flex-wrap gap-1 sm:gap-2">
            {popularTopics.map((popularTopic) => (
              <button
                key={popularTopic}
                type="button"
                onClick={() => handleTopicClick(popularTopic)}
                className="px-2 sm:px-3 py-1 text-xs sm:text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors"
                disabled={isLoading}
              >
                {popularTopic}
              </button>
            ))}
          </div>
        </div>

        {/* 分析深度 */}
        <div>
          <label htmlFor="depth" className="block text-sm font-medium text-gray-700 mb-2">
            分析深度: {depth}
          </label>
          <input
            id="depth"
            type="range"
            min="1"
            max="5"
            value={depth}
            onChange={(e) => setDepth(Number(e.target.value))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            disabled={isLoading}
          />
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>简单</span>
            <span>中等</span>
            <span>详细</span>
          </div>
        </div>

        {/* 高级选项 */}
        <div>
          <button
            type="button"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-900 transition-colors"
            disabled={isLoading}
          >
            <Settings className="w-4 h-4" />
            高级选项
            <span className={`transform transition-transform ${showAdvanced ? 'rotate-180' : ''}`}>
              ▼
            </span>
          </button>

          {showAdvanced && (
            <div className="mt-4 space-y-4 p-4 bg-gray-50 rounded-lg">
              {/* 关注分类 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  关注分类 (可选)
                </label>
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                  {categoryOptions.map((category) => (
                    <label
                      key={category.value}
                      className="flex items-center gap-2 text-sm cursor-pointer"
                    >
                      <input
                        type="checkbox"
                        checked={selectedCategories.includes(category.value)}
                        onChange={() => toggleCategory(category.value)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        disabled={isLoading}
                      />
                      <span className="text-gray-700">{category.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* 内容选项 */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <label className="flex items-center gap-2 text-sm cursor-pointer">
                  <input
                    type="checkbox"
                    checked={includeHistorical}
                    onChange={(e) => setIncludeHistorical(e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    disabled={isLoading}
                  />
                  <span className="text-gray-700">包含历史背景</span>
                </label>

                <label className="flex items-center gap-2 text-sm cursor-pointer">
                  <input
                    type="checkbox"
                    checked={includeContemporary}
                    onChange={(e) => setIncludeContemporary(e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    disabled={isLoading}
                  />
                  <span className="text-gray-700">包含当代观点</span>
                </label>
              </div>
            </div>
          )}
        </div>

        {/* 提交按钮 */}
        <div className="flex gap-3">
          <button
            type="submit"
            disabled={!topic.trim() || isLoading}
            className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-medium py-2 sm:py-3 px-4 sm:px-6 rounded-lg transition-colors flex items-center justify-center gap-2 text-sm sm:text-base"
          >
            {isLoading ? (
              <>
                <div className="w-3 sm:w-4 h-3 sm:h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                <span className="hidden sm:inline">分析中...</span>
                <span className="sm:hidden">分析中</span>
              </>
            ) : (
              <>
                <Sparkles className="w-3 sm:w-4 h-3 sm:h-4" />
                <span className="hidden sm:inline">生成知识图谱</span>
                <span className="sm:hidden">生成图谱</span>
              </>
            )}
          </button>
        </div>
      </form>

      {/* 提示信息 */}
      <div className="mt-4 p-3 bg-blue-50 rounded-lg">
        <p className="text-sm text-blue-800">
          💡 提示：分析深度越高，生成的概念网络越详细，但处理时间也会相应增加。
        </p>
      </div>
    </div>
  );
}
